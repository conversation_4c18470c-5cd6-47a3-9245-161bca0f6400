.DEFAULT_GOAL := all

.PHONY: all
all: build.multiarch

# ==============================================================================
# Includes

include build/make-rules/common.mk # make sure include common.mk at the first include line
include build/make-rules/image.mk
include build/make-rules/help.mk

# ==============================================================================
# Targets

## image: Build docker images for host arch.
.PHONY: build.amd64
amd64:
	@$(MAKE) build.amd64

## image: Build docker images for host arch.
arm64:
	@$(MAKE) build.arm64

## image.multiarch: Build docker images for multiple platforms. See option PLATFORMS.
.PHONY: build.multiarch
build:
	@$(MAKE) build.multiarch

## clean: Remove all files that are created by building.
.PHONY: clean
clean:
	@echo "===========> Cleaning all build output"
	@-rm -vrf $(OUTPUT_DIR)

## help: Show this help info.
.PHONY: help
help: Makefile
	@echo -e "\nUsage: make <TARGETS> <OPTIONS> ...\n\nTargets:"
	@sed -n 's/^##//p' $< | column -t -s ':' | sed -e 's/^/ /'
	@echo "$$USAGE_OPTIONS"

## update.scripts: update common scripts
.PHONY: update.scripts
update.scripts:
	wget http://************:81/bingokube/devops/cluster-image-update.sh
	sh ./cluster-image-update.sh && rm -rf ./cluster-image-update.sh

## upload.scripts: upload scripts
.PHONY: upload.scripts
upload.scripts:
	cp -r build/make-rules .
	zip -r cluster-image-make-rules.zip make-rules
	s3cmd put cluster-image-make-rules.zip s3://bingokube/devops
	rm -rf cluster-image-make-rules.zip make-rules