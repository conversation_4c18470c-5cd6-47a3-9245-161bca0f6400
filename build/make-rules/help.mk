# ==============================================================================
# Usage

define USAGE_OPTIONS

Options:
  REGISTRY_PREFIX  Docker registry prefix. Default is registry.bingosoft.net.
                   Example: make push REGISTRY_PREFIX=registry.bingosoft.net VERSION=v1.6.2
  VERSION          The version information compiled into binaries.
                   The default is obtained from gsemver or git.
  EXTRA_ENVS       Additional environment variables passed to the init.sh script during build.
                   Format: `--key1=value1,--key2=value2`.
                   Example: make EXTRA_ENVS="--version=v1.0.0,--arch=arm64"

Examples:
  # 构建多架构镜像
    make

  # 构建单架构集群镜像
    make amd64/arm64

  # 构建镜像并指定tag
    make VERSION=v1.0.0

  # 构建镜像并指定init.sh脚本参数
    make EXTRA_ENVS="--version=v1.0.0,--arch=arm64"
endef
export USAGE_OPTIONS