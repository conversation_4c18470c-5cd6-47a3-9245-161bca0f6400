SHELL := /bin/bash

# ==============================================================================
# Makefile helper functions for docker image
#

EXTRA_ENVS ?=

REGISTRY_PREFIX ?= registry.bingosoft.net/${PROJECT_GROUP}/cluster-image
.PHONY: clear.manifest
clear.manifest:
	@echo "===========> Clearing manifest $(PROJECT_NAME) $(VERSION)"
	kubepilot manifest delete $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION) 2>/dev/null|| true
	kubepilot manifest delete $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-arm64 2>/dev/null|| true
	kubepilot manifest delete $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-amd64 2>/dev/null|| true
	kubepilot rmi $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION) 2>/dev/null|| true

.PHONY: build.multiarch
build.multiarch: clear.manifest
	@echo "===========> Building cluster image $(PROJECT_NAME) $(VERSION)"
	kubepilot build -t $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-arm64 --platform=linux/arm64 . -e "--arch=arm64" -e "$(EXTRA_ENVS)"
	kubepilot build -t $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-amd64 --platform=linux/amd64 . -e "--arch=amd64" -e "$(EXTRA_ENVS)"
	@echo "===========> Pushing $(PROJECT_NAME) $(VERSION)"
	kubepilot push $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-amd64
	kubepilot push $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-arm64
	@echo "===========> Adding manifest for $(PROJECT_NAME) $(VERSION)"
	kubepilot manifest add $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION) $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-arm64 --arch arm64
	kubepilot manifest add $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION) $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-amd64 --arch amd64
	@echo "===========> Pushing manifest for $(PROJECT_NAME) $(VERSION)"
	kubepilot manifest push --skip-tls-verify $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)

.PHONY: build.arm64
build.arm64: clear.manifest
	@echo "===========> Building cluster image $(PROJECT_NAME) $(VERSION)"
	kubepilot build -t $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-arm64 --platform=linux/arm64 . -e "--arch=arm64" -e "$(EXTRA_ENVS)"
	@echo "===========> Pushing $(PROJECT_NAME) $(VERSION)"
	kubepilot push $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-arm64
	@echo "===========> Adding manifest for $(PROJECT_NAME) $(VERSION)"
	kubepilot manifest add $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION) $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-arm64 --arch arm64
	@echo "===========> Pushing manifest for $(PROJECT_NAME) $(VERSION)"
	kubepilot manifest push --skip-tls-verify $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)

.PHONY: build.amd64
build.amd64: clear.manifest
	@echo "===========> Building cluster image $(PROJECT_NAME) $(VERSION)"
	kubepilot build -t $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-amd64 --platform=linux/amd64 . -e "--arch=amd64" -e "$(EXTRA_ENVS)"
	@echo "===========> Pushing $(PROJECT_NAME) $(VERSION)"
	kubepilot push $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-amd64
	@echo "===========> Adding manifest for $(PROJECT_NAME) $(VERSION)"
	kubepilot manifest add $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION) $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)-amd64 --arch amd64
	@echo "===========> Pushing manifest for $(PROJECT_NAME) $(VERSION)"
	kubepilot manifest push --skip-tls-verify $(REGISTRY_PREFIX)/$(PROJECT_NAME):$(VERSION)
