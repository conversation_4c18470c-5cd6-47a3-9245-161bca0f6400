SHELL := /bin/bash

# include the common make file
COMMON_SELF_DIR := $(dir $(lastword $(MAKEFILE_LIST)))

ifeq ($(origin ROOT_DIR),undefined)
ROOT_DIR := $(abspath $(shell cd $(COMMON_SELF_DIR)/../.. && pwd -P))
endif
ifeq ($(origin OUTPUT_DIR),undefined)
OUTPUT_DIR := $(ROOT_DIR)/_output
$(shell mkdir -p $(OUTPUT_DIR))
endif
ifeq ($(origin TMP_DIR),undefined)
TMP_DIR := $(OUTPUT_DIR)/tmp
$(shell mkdir -p $(TMP_DIR))
endif

# set the version number. you should not need to do this
# for the majority of scenarios.
ifeq ($(origin VERSION), undefined)
VERSION := $(shell git symbolic-ref --short HEAD 2>/dev/null || git describe --tags --exact-match 2>/dev/null)
VERSION := $(subst /,-,$(VERSION))
endif
# Check if the tree is dirty.  default to dirty
GIT_TREE_STATE:="dirty"
ifeq (, $(shell git status --porcelain 2>/dev/null))
	GIT_TREE_STATE="clean"
endif
GIT_COMMIT:=$(shell git rev-parse HEAD)

# Minimum test coverage
ifeq ($(origin COVERAGE),undefined)
COVERAGE := 60
endif

# Set a PROJECT_GROUP
PROJECT_GROUP:=$(shell git config --get remote.origin.url | grep -Eo "(git@gitlab\.bingosoft\.net:|https://gitlab\.bingosoft\.net/)([^/]+)/" | sed -e "s|************************:||" -e "s|https://gitlab.bingosoft.net/||" -e "s|/||")

# Set the project name
PROJECT_NAME:=$(shell git remote get-url origin | awk -F/ '{print $$NF}' | sed 's/\.git$$//')

# Linux command settings
FIND := find . ! -path './third_party/*' ! -path './vendor/*'
XARGS := xargs --no-run-if-empty

# Makefile settings
ifndef V
MAKEFLAGS += --no-print-directory
endif

