# backup-kube

该项目主要是用来构建backup-kube的集群镜像


## 前置条件

- Kubernetes
- Kubepilot
- Helm

# 构建镜像
```
make VERSION=v1.0.0
```

## 安装应用

使用 Kubepilot 安装应用

```shell
kubepilot run -c registry.bingosoft.net/bingokube/cluster-image/backup-kube:v1.0.0 \
-e "backupKubeS3Host=system-bcs3.bcs3" \
-e "backupKubeAccessKey=FAAC87B6A810442F1332" \
-e "backupKubeSecretKey=WzkwNDA3RDdENzNFQUMzMTQ3OUYwN0MyQjFFQzM1" \
-e "backupKubeSchedule=0 0 * * *" \
-e "backupKubeS3backupPrefix=s3://kube-backup" \
-e "backupKubeRetainCount=10" \
-e "backupKubeVerse=false"
```
参数说明：
```shell
backupKubeRepository: 镜像仓库URL(必要参数),如果安装时没有传参数backupKubeRepository,则取集群全局参数registryURL
backupKubeVirtualNetwork: 应用部署在哪个vn（非必须参数,系统组件都需要配置该参数）
backupKubeMasterCount: master节点数量
backupKubeRetainCount: 备份保留数量,默认值10
backupKubeS3backupPrefix: 备份路径前缀,默认值 s3://kube-backup 
backupKubeEtcdSecret: kubeverse的etcd证书
backupKubeTls: 访问kubeverse etcd是否需要证书  默认值true
backupKubeVerse: 是否是kubeVerse控制集群(启用kubeverse备份必须部署在kube-system否则拿不到secret下的etcd证书) 默认值false
backupKubeAccessKey: 连接s3的accessKey
backupKubeSecretKey: 连接s3的secretKey
backupKubeS3Host: s3主机地址,例如: system-bcs3.bcs3（bcs3默认service）
backupKubeSchedule: 任务运行周期(一天一次)     
backupKubeReplicaCount: 副本数
```

## 升级应用

```shell
kubepilot upgrade -c registry.bingosoft.net/bingokube/cluster-image/backup-kube:v1.0.0 \
-e "backupKubeS3Host=system-bcs3.bcs3" \
-e "backupKubeAccessKey=FAAC87B6A810442F1332" \
-e "backupKubeSecretKey=WzkwNDA3RDdENzNFQUMzMTQ3OUYwN0MyQjFFQzM1" \
-e "backupKubeSchedule=0 0 * * *" \
-e "backupKubeS3backupPrefix=s3://kube-backup" \
-e "backupKubeRetainCount=10" \
-e "backupKubeVerse=false"
```
参数说明：同上

## 卸载应用

```shell
kubepilot uninstall -c registry.bingosoft.net/bingokube/cluster-image/backup-kube:v1.0.0
```

查看应用状态

```shell
helm -n kube-system ls 
```

