# Default values for backup-k8s.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

global:
  repository: registry.bingosoft.net
  virtualNetwork: system-vn

masterCount: 3
# 是否是kubeVerse控制集群
kubeVerse: false
# 备份保留份数
retainCount: 10
# 备份路径前缀
s3backupPrefix: "s3://kube-backup"

s3client:
  accessKey: "C23DCD4B8CF195EDCE4A"
  secretKey: "W0Y5MDRDRkZENEU1NTNDdGFGNjhCRTVFOERFNTlG"
  s3Host: "system-bcs3.bcs3"

# kubeverse etcd 证书
kubeEtcdSecret: etcd-cert-secret
# kubeverse etcd是否开启证书
kubeTls: true

image:
  repository: bingokube/backup-kube
  pullPolicy: IfNotPresent
  tag: "v1.0.2"

schedule: "0 0 * * *"
