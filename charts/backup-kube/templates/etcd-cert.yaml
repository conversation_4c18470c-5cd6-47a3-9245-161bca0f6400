apiVersion: v1
data:
  etcd-ca-key.pem: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  etcd-ca.csr: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0KTUlJQ3pqQ0NBYllDQVFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFXcHBibWN4RURBTwpCZ05WQkFjVEIwSmxhV3BwYm1jeERUQUxCZ05WQkFvVEJHVjBZMlF4RmpBVUJnTlZCQXNURFVWMFkyUWdVMlZqCmRYSnBkSGt4RFRBTEJnTlZCQU1UQkdWMFkyUXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUsKQW9JQkFRREFHeGZOb1M5UEx0bUdpMThiWWxtMVc3dFliTkFKYnlNSVl3SS9sWVhVYjJ5Uk1EN2xLSlF4UFRGaApQa2R5Yk40QlJvdjcvNmk2WVBxSkFZeDNkQ2p1VnZKRGtZaG9Vc0JJK0F0OGtPRVU0WmxpamlTK1JsMHRyV0RrCjIxcHdDVnVMQmdiazUxOGtHMjZzci9leGU4dlF3bFd0bjJDZXgxdHRkQjlWbDRULzE4R0lRT0Y3Z2YzTFNMc1IKQmRJWE82UHpzS0FMZG1PYlpOVVQ4Z0ZkUkJzcmQwY1VVeTdzOTU0UlJjMW9hdjFZdmVzRW5qTDdXbnBIY0E5SApidGlkR2sxbGw4eTM2cXhzK3kzVTM5MXpkZXFmVlljd3lwRHBkejJ3QUF6dHBKYjNrd2RGd2N2QlRpR3M2b0UyCnIwV2xvM2NnYUlLN0ZOZkJhemNiTlB2eTBIT0pBZ01CQUFHZ0lqQWdCZ2txaGtpRzl3MEJDUTR4RXpBUk1BOEcKQTFVZEV3RUIvd1FGTUFNQkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBRUQ1L2dLQnRtUDMyQk9Ec1QwQQorZ3NRSERLVDBkVFlweDhZRi9abFh5U1JVSEZtdWVXTCtHNCt2VUlQcTJPRGJ6N29GZWFnM2R6N1VSa0RSSEJTClZTektMSXhXVHB0VDZaai8vR1ZHWEN5QUozR2k0MndzSFYwcGtWcUlJMndvNXo5RU0rSDA5T2NFZEdqd2lIZUUKeTZPMkNCb2pVdHhwU2xYNGJtT3NsWnJYSFJJSzVOSTdUSnVOUWtLRHl2dVl4NURzTit4bHZ4Mi9OS3dOdTZYbwpmZ3Y0TjN4aXMxdlZhdEtnZVdzQU1LSG5BWnB1K0hlMGJ0Zm5Dd3BDWDl1WTBsUU5MN24zbmdJSmpSUGlQdWZoCmczTVFDL29ua1JRZjNNTk9UVnExWm1LTlZ0M1hqRERlU0pRaXc0ODNTNU93c2REbWV1YmhHb2hMZFpIaFR2TlcKMU9BPQotLS0tLUVORCBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0K
  etcd-ca.pem: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURvRENDQW9pZ0F3SUJBZ0lVRE9LOElYNXp4bHZyWGYzMmlHRWppWTNXbWs0d0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFXcHBibWN4RURBT0JnTlZCQWNUQjBKbAphV3BwYm1jeERUQUxCZ05WQkFvVEJHVjBZMlF4RmpBVUJnTlZCQXNURFVWMFkyUWdVMlZqZFhKcGRIa3hEVEFMCkJnTlZCQU1UQkdWMFkyUXdJQmNOTWpNeE1qSTJNVEUwTURBd1doZ1BNakV5TXpFeU1ESXhNVFF3TURCYU1HY3gKQ3pBSkJnTlZCQVlUQWtOT01SQXdEZ1lEVlFRSUV3ZENaV2xxYVc1bk1SQXdEZ1lEVlFRSEV3ZENaV2xxYVc1bgpNUTB3Q3dZRFZRUUtFd1JsZEdOa01SWXdGQVlEVlFRTEV3MUZkR05rSUZObFkzVnlhWFI1TVEwd0N3WURWUVFECkV3UmxkR05rTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF3QnNYemFFdlR5N1oKaG90ZkcySlp0VnU3V0d6UUNXOGpDR01DUDVXRjFHOXNrVEErNVNpVU1UMHhZVDVIY216ZUFVYUwrLytvdW1ENgppUUdNZDNRbzdsYnlRNUdJYUZMQVNQZ0xmSkRoRk9HWllvNGt2a1pkTGExZzVOdGFjQWxiaXdZRzVPZGZKQnR1CnJLLzNzWHZMME1KVnJaOWduc2RiYlhRZlZaZUUvOWZCaUVEaGU0SDl5MGk3RVFYU0Z6dWo4N0NnQzNaam0yVFYKRS9JQlhVUWJLM2RIRkZNdTdQZWVFVVhOYUdyOVdMM3JCSjR5KzFwNlIzQVBSMjdZblJwTlpaZk10K3FzYlBzdAoxTi9kYzNYcW4xV0hNTXFRNlhjOXNBQU03YVNXOTVNSFJjSEx3VTRock9xQk5xOUZwYU4zSUdpQ3V4VFh3V3MzCkd6VDc4dEJ6aVFJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBUVl3RHdZRFZSMFRBUUgvQkFVd0F3RUIKL3pBZEJnTlZIUTRFRmdRVU9uYlNPZzBHSlJrN2lleVY3bUFKZ0hJY2ZYOHdEUVlKS29aSWh2Y05BUUVMQlFBRApnZ0VCQUgzSklYbGtCNE5pY3h1YjZ5d05KQzVoclJZcWMvK0dNVTdFSjIvVUlTa3hxU1Q3STEyV3VIazhvb1cyCnVNV3phRjkvVEhZQmx0VGRzak5JUHJvcHA1YnBTSjIyQ3prdXNVSkdhVFZqZ1drcEdvVk83cXNGTW9YUVhwbnAKU2ZKekpyVUkwbEpUbE50dldlQytFUWQ1Y1JabFcxbnpXci9tNlBUQW85cjd4ZU10TzNqK2JMS05mL05uR2NqUwp0bHZ0bTlqR2xrbnY1WnlwU2FIZHVwOWU2cEFMZk1zY3VxSjJQOWFuaW1PaU9JWTRUT2RkVkdmWWkxbGx4MlBHClE2dE5hbTdYTEJzUXZoaFhKTDNNQWtrVjNCeUJWUy9PNnlBOWlST1dOQXJzVy9tVmNTWFhzbCtEUGV5R2g0L00KRzVQSEhqTWpvLzd0MGZ4YlB5aVh1ZGFTdmVZPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
  etcd-key.pem: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  etcd.csr: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0KTUlJRDN6Q0NBc2NDQVFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFXcHBibWN4RURBTwpCZ05WQkFjVEIwSmxhV3BwYm1jeERUQUxCZ05WQkFvVEJHVjBZMlF4RmpBVUJnTlZCQXNURFVWMFkyUWdVMlZqCmRYSnBkSGt4RFRBTEJnTlZCQU1UQkdWMFkyUXdnZ0VpTUEwR0NTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUsKQW9JQkFRQ2tTcHptbzdhRHE1OFhSZXVnazBtRGhXS0EvRVpTaiswZ1oyVFM5WWZkWU1oUms3Q3dHdk1GK1ZsUwo5WWlkQU9mZlJ0azAvRHcvcWViUmRKUnkreWQ2Z0FrdFRRdHI1SzFyVXlldVNPcERjL0cvU0c3d2tidmpWL25QCllUaUpmM1NhMnZuTjNxUXhSSEdHVWtwLzQrdktwNk83TVhrems0Z3NFNUtONDI5blVoMGhtZ3ZQcFJ1bTBJZlAKTjJlUk56SGgzblZ5T1k5RXRVNmV5aVVMbWRyMjQ5bGRIdEtIRUpJdi9JcHVWVVoxNGVkT2JiQXRQeW8vRTJzdgoyZjY4VmtJYjY4MHJGdGRmWE5obVZCME9oemkxZXNWcWl6SnRyT0ZCTURZWmFwc1RCbDhab1lvU05NOU5SOVNhCjFkSnFkSnUra21yd3JtUU5uMmF6MWNNaGh1TDlBZ01CQUFHZ2dnRXhNSUlCTFFZSktvWklodmNOQVFrT01ZSUIKSGpDQ0FSb3dnZ0VXQmdOVkhSRUVnZ0VOTUlJQkNZSkNaWFJqWkMxamJIVnpkR1Z5TFRBdVpYUmpaQzFqYkhWegpkR1Z5TFdobFlXUnNaWE56TG10MVltVXRjM2x6ZEdWdExuTjJZeTVqYkhWemRHVnlMbXh2WTJGc2drSmxkR05rCkxXTnNkWE4wWlhJdE1TNWxkR05rTFdOc2RYTjBaWEl0YUdWaFpHeGxjM011YTNWaVpTMXplWE4wWlcwdWMzWmoKTG1Oc2RYTjBaWEl1Ykc5allXeUNRbVYwWTJRdFkyeDFjM1JsY2kweUxtVjBZMlF0WTJ4MWMzUmxjaTFvWldGawpiR1Z6Y3k1cmRXSmxMWE41YzNSbGJTNXpkbU11WTJ4MWMzUmxjaTVzYjJOaGJJSXpaWFJqWkMxamJIVnpkR1Z5CkxXaGxZV1JzWlhOekxtdDFZbVV0YzNsemRHVnRMbk4yWXk1amJIVnpkR1Z5TG14dlkyRnNnZ0NIQkg4QUFBRXcKRFFZSktvWklodmNOQVFFTEJRQURnZ0VCQUNtU2hSZnA2bVNraTJsNmI0WWlIeTNyeWxVekM0WFQ4aWw5ekRCZQpuTUNoM2dFRDNhYnEyUW9vZEV2Y0JhVi81aE9sdHNqUkFCSUY3YmVCRGEzMTljUlFZVDVuYWxMRlZKc3lWNzlMCjA0L0l5L25zYlZ0SHlLSUhRZXAzNE8zMlJMMVdzRll0SFFGRW40YXJ0WU5iVTF5Nml1bzZ6ak1FUkhZTVhnOTYKTTl4MnBvRGtKNzlMOUF2YWZMWnFEclBydXRmd1dnT1JrNWNFVEZzTTJlSVp3SnRteThkNDIvY3VraElyVkl6OApkdU5iKzVUTjZia3ZXVEhhb3VRbmJCejR0dTlkNEFTOXBLd25NTTVoRzBtaFY4bGJLb1N2MktRdnQyNFM1UklrCmFlVU9mZGcvMHROeFI1aVcwRmppVkhYRnBQN3BFdzJBOXE2aE1ReVlDR1VaT2gwPQotLS0tLUVORCBDRVJUSUZJQ0FURSBSRVFVRVNULS0tLS0K
  etcd.pem: 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
  tls.crt: 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
  tls.key: 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
kind: Secret
metadata:
  name: etcd-cert
  namespace: kube-system
type: kubernetes.io/tls
