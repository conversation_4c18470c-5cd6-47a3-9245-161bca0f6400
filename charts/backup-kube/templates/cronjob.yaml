apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ .Chart.Name }}
  namespace: {{ $.Release.Namespace }}
spec:
  schedule: {{ $.Values.schedule | quote }}
  concurrencyPolicy: Allow
  startingDeadlineSeconds: 200
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      backoffLimit: 2
      parallelism: {{ $.Values.masterCount }}
      template:
        metadata:
          annotations:
            {{- if ne .Values.global.virtualNetwork "" }}
            bingokube-nvs.bingosoft.net/networkInterfaces: '{"networkInterface":[{"virtualNetworkId":"{{ .Values.global.virtualNetwork }}"}]}'
            {{- end }}
          labels:
            bingokube.bingosoft.net/app: "backup-kube"
        spec:
          affinity:
            podAntiAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                - topologyKey: kubernetes.io/hostname
                  labelSelector:
                    matchExpressions:
                      - key: "bingokube.bingosoft.net/app"
                        operator: In
                        values:
                          - "backup-kube"
          tolerations:
            - effect: NoSchedule
              key: node-role.kubernetes.io/master
          nodeSelector:
            node-role.kubernetes.io/master: ""
          containers:
            - name: backup
              image: "{{ .Values.global.repository }}/{{ $.Values.image.repository }}:{{ $.Values.image.tag }}"
              imagePullPolicy: {{ $.Values.image.pullPolicy }}
              command:
                - "sh"
                - "/app/kube-backup.sh"
                - --kubeVerse="{{ .Values.kubeVerse }}"
                - --retainCount="{{ .Values.retainCount }}"
                - --s3backupPrefix="{{ .Values.s3backupPrefix }}"
                - --accessKey="{{ .Values.s3client.accessKey }}"
                - --secretKey="{{ .Values.s3client.secretKey }}"
                - --s3Host="{{ .Values.s3client.s3Host }}"
                - --region="{{ .Values.s3client.region }}"
                - --kubeTls="{{ .Values.kubeTls }}"
              env:
                - name: ETCD_ENDPOINTS
                  value: ${ETCD_ENDPOINTS}
                - name: NODE_NAME
                  valueFrom:
                    fieldRef:
                      fieldPath: spec.nodeName
                - name: HOST_IP
                  valueFrom:
                    fieldRef:
                      fieldPath: status.hostIP
              volumeMounts:
                - mountPath: /etc/kubernetes/
                  name: kubernetes
                - mountPath: /var/lib/kubelet
                  name: kubelet
                {{ if .Values.kubeVerse }}
                - mountPath: /home/<USER>
                  name: kube-etcd-cert
                {{ end }}
          restartPolicy: OnFailure
          volumes:
            - name: kubernetes
              hostPath:
                path: /etc/kubernetes/
                type: DirectoryOrCreate
            - name: kubelet
              hostPath:
                path: /var/lib/kubelet/
                type: DirectoryOrCreate
            {{ if .Values.kubeVerse }}
            - name: kube-etcd-cert
              secret:
                defaultMode: 420
                secretName: {{ .Values.kubeEtcdSecret }}
            {{ end }}
