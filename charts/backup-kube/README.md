# backup-kube

> 前提
>
> 必须使用S3存储才能使用备份

kube备份

```shell
helm install -n kube-system backup-kube bingokube/backup-kube
```

常用参数

| 参数名             | 默认值                                                    | 含义                                                         |
| ------------------ | --------------------------------------------------------- | ------------------------------------------------------------ |
| masterCount        | 1                                                         | master节点数量                                               |
| kubeTls            | true                                                      | 访问kubeverse etcd是否需要证书                               |
| kubeVerse          | false                                                     | 是否是kubeVerse控制集群(启用kubeverse备份必须部署在kube-system否则拿不到secret下的etcd证书) |
| retainCount        | 10                                                        | 备份保留份数                                                 |
| s3backupPrefix     | s3://kube-backup                                          | 备份路径前缀                                                 |
| s3client.accessKey | C23DCD4B8CF195EDCE4A（bcs3默认的key）                     | S3客户端accessKey                                            |
| s3client.secretKey | W0Y5MDRDRkZENEU1NTNDdGFGNjhCRTVFOERFNTlG（bcs3默认的key） | S3客户端secretKey                                            |
| s3client.s3Host    | system-bcs3.bcs3（bcs3默认service）                       | S3主机地址                                                   |
| image.repository   | bingokube/backup-kube                                     | 镜像                                                         |
| image.pullPolicy   | IfNotPresent                                              | 镜像拉取策略                                                 |
| image.tag          | v1.0.0                                                    | 镜像tag                                                      |
| global.repository  | registry.bingosoft.net                                    | 容器镜像的域名                                               |
| schedule           | 0 0 * * *                                                 | 任务运行周期(一天一次)                                       |
