---
type: "manual"
---

您是一位专注于研发SHELL 的专家级 AI 程序员

## 注意事项
- 要求中文注释
- 要求兼容性要好

## 首选库
> 工具函数实现以及代码风格优先参考以下库

- [Kubernetes的hack](https://github.com/kubernetes/minikube/tree/master/hack)
- [Minikube的hack]https://github.com/kubernetes/minikube/tree/master/hack

## 规范

### 文件头部规范

```
#!/usr/bin/env bash
# 脚本用途说明
# 脚本使用说明
```

### 基本设置
```
set -o errexit  # 发生错误立即退出
set -o nounset  # 使用未定义的变量时报错
set -o pipefail # 管道中的任何命令失败都会导致整个管道失败

# 清除不需要的环境变量
unset CDPATH
```
### 变量命名规范
#### 常量定义

```
# 只读常量使用 readonly 声明
readonly KUBE_BUILD_IMAGE_REPO=kube-build

# 带默认值的环境变量
DOCKER_OPTS=${DOCKER_OPTS:-""}
```

#### 路径变量
```
# 规范化路径
KUBE_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")"/.. && pwd -P)

# 输出目录定义
readonly LOCAL_OUTPUT_ROOT="${KUBE_ROOT}/${OUT_DIR:-_output}"
readonly LOCAL_OUTPUT_SUBPATH="${LOCAL_OUTPUT_ROOT}/dockerized"
```

### 数组定义
```
# 命令数组
IFS=" " read -r -a DOCKER <<< "docker ${DOCKER_OPTS}"

# 目录数组
declare -a VOLUME_DIRS=(
  "${LOCAL_OUTPUT_ROOT}"
  "${KUBE_ROOT}/pkg"
  "${KUBE_ROOT}/cmd"
)
```

### 函数定义规范

#### 基本函数结构
```
# 函数名使用小写字母加下划线
# 函数开头要有函数说明注释、参数说明、返回值、使用示例
function function_name() {
  # 局部变量声明
  local target=$1
  local local_dir="${2:-}"

  # 参数验证
  [[ -n "${target}" ]] || {
    kube::log::error "Target required"
    return 1
  }

  # 函数逻辑

  # 返回值
  return 0
}
```


### 错误处理
```
# 使用trap捕获信号
trap cleanup EXIT

# 错误处理函数
function error() {
  local msg=$1
  echo "ERROR: ${msg}" >&2
  exit 1
}

# 检查命令返回值
if ! command; then
  error "command failed"
fi
```

### 日志输出

```
# 日志级别函数
log_info() { echo "INFO: $@" >&2; }
log_error() { echo "ERROR: $@" >&2; }

```

### 基础环境检查及参数验证
```
# 基础环境检查，如命令是否存在等
if ! command -v ctr >/dev/null 2>&1; then
  echo "ERROR ctr not found. Aborting."
  exit 1
fi

# 必要参数检查
if [[ $# -lt 1 ]]; then
  echo "Usage: $0 <required-arg>" >&2
  exit 1
fi

# 参数合法性检查
if [[ ! -f "${INPUT_FILE}" ]]; then
  echo "Input file does not exist: ${INPUT_FILE}"
  exit 1
fi


```

### 代码组织
```
# 1. 常量定义
# 2. 函数定义
# 3. 主逻辑
# 4. 清理函数

# 主逻辑放在最后,使用main函数包装
function main() {
  # 解析参数
  # 执行操作
  # 清理资源
}

main "$@"
```

### 注释规范
```
# 重要函数和复杂逻辑需要详细注释
# 注释要说明:
# - 功能是什么
# - 参数含义
# - 返回值
# - 注意事项
```

### 命名规范

- 文件名全小写,使用连字符分隔: build-image.sh
- 函数名小写加下划线: build_image()
- 常量大写加下划线: MAX_RETRIES
- 变量小写加下划线: image_name

### 安全实践
```
# 使用 mktemp 创建临时文件
TEMP_FILE=$(mktemp)

# 使用 mktemp 创建临时目录
TEMP_DIR="$(mktemp -d)

# 设置安全的文件权限
chmod 600 "${TEMP_FILE}"

# 检查命令执行结果
if ! command; then
  kube::log::error "Command failed"
  return 1
fi

# 4. 使用引号避免注入
docker run --rm -v "${src}":"${dest}"

# 5. 检查文件是否存在后再操作
[[ -f "${FILE}" ]] && rm "${FILE}"
```

### 最佳实践

- 使用 $(command) 而不是反引号
- 使用 [[]] 进行条件测试
- 使用 ${var} 引用变量
- 字符串和变量都要用引号包裹
- 缩进使用2个空格
- 优先使用内置命令
- 函数返回值使用 return
- 每个文件都应该是可执行的
- 关键点要有注释说明
- 通过`mktemp -d`创建临时目录，执行脚本过程中产生的临时文件放到此处，并在函数结束时清理


