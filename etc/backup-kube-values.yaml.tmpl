# 全局参数 #
global:
    repository: "{{ if .backupKubeRepository }}{{ .backupKubeRepository }}{{ else }}{{ .registryURL }}{{ end }}"
    virtualNetwork: "{{ if .backupKubeVirtualNetwork }}{{ .backupKubeVirtualNetwork }}{{ else }}{{ .virtualNetwork }}{{end}}"

{{ if .backupKubeReplicaCount }}replicaCount: {{ .backupKubeReplicaCount }}{{ end }}
{{ if .backupKubeMasterCount }}masterCount: {{ .backupKubeMasterCount }}{{ end }}
{{ if .backupKubeRetainCount }}retainCount: {{ .backupKubeRetainCount }}{{ end }}
{{ if .backupKubeS3backupPrefix }}s3backupPrefix: {{ .backupKubeS3backupPrefix }}{{ end }}
{{ if .backupKubeEtcdSecret }}kubeEtcdSecret: {{ .backupKubeEtcdSecret }}{{ end }}

kubeVerse: {{ if eq .backup<PERSON><PERSON><PERSON>erse true }}true{{ else }}false{{ end }}

s3client:
    s3Host: {{ if .backupKubeS3Host }}{{ .backupKubeS3Host }}{{ else }}system-bcs3.bcs3{{ end }}
    accessKey: {{ if .backupKubeAccessKey }}{{ .backupKubeAccessKey }}{{ else }}C23DCD4B8CF195EDCE4A{{ end }}
    secretKey: {{ if .backupKubeSecretKey }}{{ .backupKubeSecretKey }}{{ else }}W0Y5MDRDRkZENEU1NTNDdGFGNjhCRTVFOERFNTlG{{ end }}

kubeTls: {{ if eq .backupKubeTls false }}false{{ else }}true{{ end }}
{{ if .backupKubeSchedule }}schedule: "{{ .backupKubeSchedule }}"{{ end }}




